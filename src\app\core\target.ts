import Element from "@/app/core/element";
import Entry from "@/app/core/entry";
import h from "@/app/helpers/all";
import parseLabelAliases from "./alias";

/**
 * Represents a target element or chain of elements for Cypress testing.
 * Handles parsing of complex selectors, aliases, and element hierarchies.
 */
export default class Target {
  /** Whether the target should be visible when located */
  public isVisible: boolean = true;

  /** Descriptor for the target (currently unused but reserved for future functionality) */
  public descriptor: any;

  /** Current entity field value (reserved for future functionality) */
  public currentEntityFieldValue: any;

  /** Single element reference (legacy property, consider deprecating) */
  public element: Element | undefined;

  /** Array of Element instances representing the target hierarchy */
  public readonly elements: Element[] = [];

  /** Reference to the last element in the hierarchy for quick access */
  public lastElement: Element | undefined;

  /**
   * Creates a new Target instance.
   *
   * @param label - The target selector string to parse
   * @param isPartial - Whether this is a partial target (affects element resolution)
   */
  constructor(
    private readonly label: string = "",
    public readonly isPartial: boolean = false
  ) {
    // Early return for recorder file selectors - these are handled differently
    if (this.label?.startsWith(Cypress.sdt.config.recorderFileSelector)) {
      return;
    }
    this.parse(label);
  }

  /**
   * Parses the target label string and processes visibility modifiers, aliases, and element hierarchies.
   *
   * @param label - The label string to parse
   * @private
   */
  private parse(label: string): void {
    if (!label) {
      return;
    }

    // Check for visibility modifier and update state accordingly
    if (label.match(/not *visible/gi)) {
      this.isVisible = false;
      label = label.replace(/not *visible/gi, "");
    }

    // Process escaped characters and quoted strings
    label = h.replaceEscapedCharactersAndQuotedStrings(label);

    // Expand entry using the Entry parser
    const entry = new Entry(label);
    const expandedLabel = entry.expandedEntry ?? "";

    // Parse and resolve aliases
    const parsedLabel = parseLabelAliases(
      expandedLabel,
      Cypress.sdt.data.aliases,
      Cypress.sdt.results.usedAliases
    );

    this.setElements(parsedLabel);
  }

  /**
   * Creates Element instances from a parsed label string and populates the elements array.
   * Handles hierarchical element relationships using the '>' separator.
   *
   * @param label - The parsed label string containing element selectors
   * @private
   */
  private setElements(label: string): void {
    if (!label) return;

    // Split label by '>' separator, but ignore '>' inside curly braces
    const elementLabels = label.split(/ *> *(?![^{]*})/);

    elementLabels.forEach((elementLabel: string, index: number) => {
      const isLast = index === elementLabels.length - 1;
      const element = new Element({
        label: elementLabel,
        isVisible: this.isVisible,
        isLast,
      });
      this.elements.push(element);
    });

    // Set reference to last element for quick access
    // Using array indexing instead of .at() for better compatibility
    this.lastElement = this.elements[this.elements.length - 1];
    if (this.lastElement) {
      this.lastElement.isLast = true;
    }
  }

  /**
   * Retrieves the Cypress element(s) represented by this target.
   * Handles recursive traversal for hierarchical element structures.
   *
   * @param param - Optional parameters for element retrieval
   * @param param.parent - Parent element to search within (defaults to body)
   * @param param.elementIndex - Index of current element in hierarchy (for recursion)
   * @param param.validation - Validation function to apply to final element
   * @returns Cypress chainable element
   */
  public get(param?: {
    parent?: Cypress.Chainable<JQuery<HTMLElement>>;
    elementIndex?: number;
    validation?: (element: JQuery<HTMLElement>) => void;
  }): Cypress.Chainable<JQuery<HTMLElement>> {
    const parent = param?.parent ?? cy.get("body");
    const elementIndex = param?.elementIndex ?? 0;
    const validation = param?.validation;

    // Handle recorder file selectors - these bypass normal element processing
    if (this.label?.startsWith(Cypress.sdt.config.recorderFileSelector)) {
      return cy.get(this.label.substring(1));
    }

    const element = this.elements[elementIndex];
    if (!element) {
      throw new Error(`No element found at index ${elementIndex}`);
    }

    // Handle direct CSS selector references (starting with @)
    if (element.label.startsWith("@")) {
      const regex = /@\((.*?)\)/;
      const match = element.label.match(regex);
      if (!match || !match[1]) {
        throw new Error(`Invalid selector format: ${element.label}`);
      }
      return cy.get(match[1]);
    }

    const lastElementIndex = this.elements.length - 1;

    return element.get(parent, validation).then((foundElement: any) => {
      if (elementIndex < lastElementIndex) {
        // Recursively traverse to next element in hierarchy
        return this.get({
          parent: cy.get(foundElement),
          elementIndex: elementIndex + 1,
          validation,
        });
      }
      return cy.get(foundElement);
    });
  }

  /**
   * Checks if the last element's name includes the specified element name.
   * Performs normalized string comparison (case-insensitive, whitespace-normalized).
   *
   * @param elementName - The element name to search for
   * @returns True if the last element's name includes the specified name
   */
  public lastElementNameIncludes(elementName: string): boolean {
    const lastElementName = this.lastElement?.name?.replace(/{|}/g, "");
    if (!lastElementName) {
      return false;
    }
    return h.includesNormalizedString(lastElementName, elementName);
  }

  /**
   * Checks if the last element's name exactly matches the specified element name.
   * Performs normalized string comparison (case-insensitive, whitespace-normalized).
   *
   * @param elementName - The element name to compare against
   * @returns True if the last element's name matches the specified name
   */
  public lastElementNameIs(elementName: string): boolean {
    const lastElementName = this.lastElement?.name?.replace(/{|}/g, "");
    if (!lastElementName) {
      return false;
    }
    return h.compareNormalizedStrings(lastElementName, elementName);
  }

  /**
   * Gets the original label string used to create this target.
   *
   * @returns The original label string
   */
  public getLabel(): string {
    return this.label;
  }
}
