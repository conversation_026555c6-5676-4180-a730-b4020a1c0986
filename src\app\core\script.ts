import { IStep } from "@/app/core/step";
import h from "@/app/helpers/all";

/**
 * Interface defining the structure of a script data object as stored in the data source
 */
interface IScriptData {
  /** The script identifier/name */
  script: string;
  /** Input parameters definition (comma-separated parameter names) */
  input?: string;
  /** Array of steps that make up this script */
  steps: IStep[];
  /** Unique identifier for the script */
  id?: string | number;
  /** Additional dynamic properties */
  [key: string]: any;
}

/**
 * Interface defining the constructor parameter
 */
interface IStepReference {
  /** The target script name to find and load */
  target?: string;
  /** The row number context for nested script execution */
  rowNumber?: string | number;
}

/**
 * Script class manages the loading, parameter replacement, and execution preparation
 * of reusable script components within the SDT (Script Driven Testing) framework.
 *
 * Scripts are reusable sequences of test steps that can be called from tests or other scripts,
 * supporting parameterization for dynamic behavior.
 */
export default class Script {
  /** The name/identifier of the loaded script */
  name?: string;

  /** Input parameter definition string (comma-separated parameter names) */
  input?: string;

  /** Array of steps that make up this script */
  steps: IStep[];

  /**
   * Creates a new Script instance by loading script data based on the provided step reference.
   *
   * @param step - Reference containing the target script name and row number context
   * @throws {Error} When the specified script cannot be found
   */
  constructor(step: IStepReference) {
    this.steps = [];
    this.set(step.target, step.rowNumber);
  }

  /**
   * Loads and initializes script data by searching for the specified script name.
   *
   * The search strategy follows this priority order:
   * 1. If scriptName contains a dot (.), search in the specific sheet after the dot
   * 2. Search in a sheet with the same name as the script
   * 3. Search across all sheets globally
   *
   * @param scriptName - The name of the script to find (may include sheet prefix like "Sheet.ScriptName")
   * @param stepRowNumber - The row number context for nested execution tracking
   * @throws {Error} When the specified script cannot be found in any location
   */
  private set(scriptName?: string, stepRowNumber?: string | number): void {
    if (!scriptName) {
      throw new Error("Script name is required");
    }

    let foundScript: IScriptData | undefined;

    // Strategy 1: Search in specific sheet if script name contains a dot
    if (scriptName.includes(".")) {
      const sheetName = scriptName.slice(scriptName.indexOf(".") + 1).trim();
      const sheetScripts = Cypress.sdt.data.scripts[sheetName];

      if (sheetScripts && Array.isArray(sheetScripts)) {
        foundScript = sheetScripts.find((script: IScriptData) =>
          h.compareNormalizedStrings(script.script, scriptName)
        );
      }
    }

    // Strategy 2: Search in sheet with same name as script
    if (!foundScript && Cypress.sdt.data.scripts[scriptName]) {
      const sheetScripts = Cypress.sdt.data.scripts[scriptName];

      if (Array.isArray(sheetScripts)) {
        foundScript = sheetScripts.find((script: IScriptData) =>
          h.compareNormalizedStrings(script.script, scriptName)
        );
      } else {
        // Handle case where scripts[scriptName] is an object, not an array
        foundScript = Object.values(sheetScripts).find(
          (script: any) =>
            script && h.compareNormalizedStrings(script.script, scriptName)
        ) as IScriptData;
      }
    }

    // Strategy 3: Global search across all sheets
    if (!foundScript) {
      const allScripts = Object.values(Cypress.sdt.data.scripts)
        .flat()
        .filter((script): script is IScriptData => script != null);

      foundScript = allScripts.find((script: IScriptData) =>
        h.compareNormalizedStrings(script.script, scriptName)
      );
    }

    if (!foundScript) {
      throw new Error(`Script not found: ${scriptName}`);
    }

    // Clone the script to avoid modifying the original data
    const clonedScript = structuredClone(foundScript);

    // Initialize instance properties
    this.name = clonedScript.script;
    this.input = clonedScript.input;
    this.steps = clonedScript.steps.map((step: IStep) => {
      // Append parent row number to create hierarchical row numbering
      if (step.rowNumber && stepRowNumber) {
        step.rowNumber = `${stepRowNumber}.${step.rowNumber}`;
      }
      return step;
    });
  }

  /**
   * Replaces parameter placeholders in script steps with provided values.
   *
   * This method performs parameter substitution by:
   * 1. Restoring escaped characters and quoted strings in the provided values
   * 2. Mapping input parameters to their corresponding values
   * 3. Replacing parameter placeholders in step targets and values using regex
   *
   * Parameter placeholders in the script are expected to match the parameter names
   * defined in the script's input property (e.g., "$param1", "$param2").
   *
   * @param values - Array of string values to substitute for script parameters
   */
  replaceParams(values: string[]): void {
    if (!values || values.length === 0) {
      return;
    }

    // Restore escaped characters and quoted strings in the provided values
    const restoredValues = values.map((value: string) =>
      h.restoreQuotedStringsAndEscapedCharacters(value)
    );

    // Create parameter-value pairs from input definition and provided values
    const paramsArgs = this.input
      ?.split(",")
      .map((param: string, index: number): [string, string] => [
        param.trim(),
        restoredValues[index]?.trim() || "",
      ])
      .filter((paramArg: [string, string]) => paramArg[1] !== "");

    if (!paramsArgs || paramsArgs.length === 0) {
      return;
    }

    // Replace parameter placeholders in each step
    this.steps = this.steps.map((step: IStep) => {
      paramsArgs.forEach((paramArg: [string, string]) => {
        const [paramName, paramValue] = paramArg;

        // Create regex pattern to match parameter placeholder (escape $ character)
        const regexPattern = new RegExp(paramName.replace("$", "\\$"), "ig");

        // Replace parameter in step target
        if (step.target) {
          step.target = step.target.replace(regexPattern, paramValue);
        }

        // Replace parameter in step values
        if (step.values) {
          step.values = step.values.replace(regexPattern, paramValue);
        }
      });
      return step;
    });
  }
}
