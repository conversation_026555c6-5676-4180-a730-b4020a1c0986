import js from "@eslint/js";
import tseslint from "typescript-eslint";

export default [
  js.configs.recommended,
  ...tseslint.configs.recommended,
  {
    files: ["**/*.{js,mjs,cjs,ts}"],
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: "module",
    },
    rules: {
      // TypeScript specific rules
      "@typescript-eslint/no-unused-vars": [
        "warn",
        { argsIgnorePattern: "^_" },
      ],
      "@typescript-eslint/explicit-function-return-type": "off",
      "@typescript-eslint/explicit-module-boundary-types": "off",
      "@typescript-eslint/no-explicit-any": "off",

      // General JavaScript/TypeScript rules
      "no-debugger": "warn",
      "prefer-const": "warn",
      "no-var": "warn",
      "no-console": "off",

      // Code style - more lenient for existing code
      indent: "off",
      quotes: "off",
      semi: "off",
      "comma-dangle": "off",
    },
  },
  {
    ignores: [
      "node_modules/**",
      "dist/**",
      "build/**",
      "cypress/downloads/**",
      "cypress/screenshots/**",
      "cypress/videos/**",
      ".history/**",
    ],
  },
];
