import Screenshot from "@/app/core/screenshot";
import cypressHelper from "@/app/helpers/cypress";

/**
 * Configuration constants for better maintainability and type safety
 */
const CONFIG_VALUES = {
  YES: "yes",
  NO: "no",
} as const;

/**
 * Utility function to safely check boolean configuration values
 * Handles case-insensitive string comparison and null/undefined values
 *
 * @param value - The configuration value to check
 * @returns true if the value represents "yes", false otherwise
 */
function isConfigEnabled(value: string | undefined | null): boolean {
  return value?.toLowerCase().trim() === CONFIG_VALUES.YES;
}

/**
 * Utility function to ensure configuration has a default value
 *
 * @param currentValue - The current configuration value
 * @param defaultValue - The default value to use if current is undefined
 * @returns The current value or the default
 */
function ensureConfigDefault(
  currentValue: string | undefined,
  defaultValue: string
): string {
  return currentValue ?? defaultValue;
}

/**
 * SDT Test Lifecycle Setup Hooks
 *
 * This module provides the core setup hooks for the SDT framework.
 * These hooks integrate with Cypress's test lifecycle to provide:
 * - Test result reporting and file management
 * - Screenshot capture on errors
 * - Domain state management
 * - Test execution tracking and error aggregation
 */
export default {
  /**
   * Before Hook - Runs once before all tests
   *
   * Responsibilities:
   * - Initialize result file writing configuration
   * - Set up result folder structure if reporting is enabled
   *
   * @throws {Error} If report folder setup fails
   */
  before(): void {
    try {
      // Ensure writeResultsToFile has a default value
      Cypress.sdt.config.writeResultsToFile = ensureConfigDefault(
        Cypress.sdt.config.writeResultsToFile,
        CONFIG_VALUES.YES
      );

      // Set up result folder if reporting is enabled
      if (isConfigEnabled(Cypress.sdt.config.writeResultsToFile)) {
        Cypress.sdt.report.setFolder();
      }
    } catch (error) {
      console.error("Error in setup.before():", error);
      throw new Error(
        `Failed to initialize test setup: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  },

  /**
   * BeforeEach Hook - Runs before each individual test
   *
   * Responsibilities:
   * - Reset domain state to ensure test isolation
   * - Clear any residual data from previous tests
   *
   * @throws {Error} If domain reset fails
   */
  beforeEach(): void {
    try {
      Cypress.sdt.resetDomain();
    } catch (error) {
      console.error("Error in setup.beforeEach():", error);
      throw new Error(
        `Failed to reset domain state: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  },

  /**
   * AfterEach Hook - Runs after each individual test
   *
   * Responsibilities:
   * - Capture screenshots on test step errors (if configured)
   * - Handle error state cleanup
   *
   * Note: Errors in this hook are logged but not re-thrown to avoid
   * masking the original test failure
   */
  afterEach(): void {
    try {
      // Take screenshot if current step has error and screenshot on error is enabled
      const hasStepError = Cypress.sdt.current?.step?.hasError;
      const screenshotEnabled = isConfigEnabled(
        Cypress.sdt.config.takeScreenshotOnError
      );

      if (hasStepError && screenshotEnabled) {
        Screenshot.takeScreenshot();
      }
    } catch (error) {
      // Log error but don't re-throw to avoid masking original test failure
      console.error("Error in setup.afterEach():", error);
      cypressHelper.clog("Screenshot capture failed", error);
    }
  },

  /**
   * After Hook - Runs once after all tests complete
   *
   * Responsibilities:
   * - Aggregate test results and identify tests with errors
   * - Generate final test reports and logs
   * - Write results to files if configured
   * - Clean up temporary resources
   *
   * @throws {Error} If critical cleanup operations fail
   */
  after(): void {
    try {
      // Aggregate tests that contain steps with errors
      Cypress.sdt.results.testsWithError =
        Cypress.sdt.results.executedTests.filter((test: any) =>
          test.steps?.some((step: any) => step.hasError)
        );

      // Log final SDT state for debugging
      cypressHelper.clog("SDT", Cypress.sdt);

      // Ensure writeResultsToFile configuration is set
      Cypress.sdt.config.writeResultsToFile = ensureConfigDefault(
        Cypress.sdt.config.writeResultsToFile,
        CONFIG_VALUES.YES
      );

      // Write results to file if enabled
      if (isConfigEnabled(Cypress.sdt.config.writeResultsToFile)) {
        Cypress.sdt.report.write();
      }
    } catch (error) {
      console.error("Error in setup.after():", error);
      // Re-throw critical errors that prevent proper cleanup
      throw new Error(
        `Failed to complete test cleanup: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  },
};
