import coreLocators from "@/app/core/locators";
import h from "@/app/helpers/all";

/**
 * Interface defining the structure of an icon object
 */
interface Icon {
  left?: string;
  right?: string;
}

/**
 * Interface defining the structure of element data from the elements collection
 */
interface ElementData {
  selector: string;
  locator?: string;
  root?: string;
}

/**
 * Interface defining the constructor parameters for Element
 */
interface ElementConstructorParams {
  /** The label string containing element definition and content */
  label: string;
  /** Whether the element should be visible (default: true) */
  isVisible?: boolean;
  /** Whether this is the last element in a chain (default: true) */
  isLast?: boolean;
}

/**
 * Represents a UI element for Cypress testing with support for:
 * - Element definitions from data collections
 * - Dynamic parameter substitution
 * - Visibility handling
 * - Icon integration
 * - Index-based selection
 * - Content matching
 *
 *
 * @example Label Format Examples:
 * ```
 * // Basic element from collection
 * "{Button}"                          // Element definition from data collection
 * "{Input}"                           // Input element from collection
 *
 * // Element with parameters
 * "{Button(submit, primary)}"         // Element with two parameters
 * "{Input(email, required)}"          // Input with type and validation params
 *
 * // Element with content matching
 * "{Button} Click Me"                 // Button element containing "Click Me" text
 * "{Link} Home"                       // Link element containing "Home" text
 *
 * // Element with index selection
 * "{Button} #2#"                      // Second button element (1-based index)
 * "{Input} #3#"                       // Third input element
 *
 * // Element with index and content
 * "{Button} #2# Submit"               // Second button containing "Submit" text
 * "{Link} #1# Dashboard"              // First link containing "Dashboard" text
 *
 * // Visibility modifiers
 * "not visible {Button}"              // Hidden button element
 * "not visible {Modal}"               // Hidden modal element
 *
 * // Complex combinations
 * "{Form(login)} not visible"         // Hidden form with login parameter
 * "{Button(primary)} #2# Save"        // Second primary button with "Save" text
 * ```
 */
export default class Element {
  /** Original label string passed to constructor */
  public label: string;

  /** Whether the element should be visible when located */
  public isVisible: boolean;

  /** Whether this is the last element in a chain (affects validation timing) */
  public isLast: boolean;

  /** Key of the element in the elements collection (if found) */
  public key?: string;

  /** Text content to match within the element */
  public content?: string;

  /** Icon configuration for the element */
  public icon?: Icon;

  /** 1-based index for selecting specific element from multiple matches */
  public index?: string;

  /** Element name extracted from label (includes braces and parameters) */
  public name?: string;

  /** CSS selector for locating the element */
  public selector?: string;

  /** Locator strategy to use (e.g., 'byContent', custom locators) */
  public locator?: string;

  /** Root element to search within (default: 'body') */
  public root?: string;

  /**
   * Creates a new Element instance
   * @param params - Constructor parameters
   */
  constructor({
    label,
    isVisible = true,
    isLast = true,
  }: ElementConstructorParams) {
    this.label = label;
    this.isVisible = isVisible;
    this.isLast = isLast;

    // Handle "not visible" modifier in label
    if (this.label.match(/not *visible/i)) {
      this.isVisible = false;
      this.label = this.label.replace(/not *visible/gi, "");
    }

    // Restore escaped characters from the label
    this.label = h.restoreQuotedStringsAndEscapedCharacters(this.label);

    // Initialize element properties
    this.setElement();
    this.setIndex();
    this.setContent();
  }

  /**
   * Parses the label to extract element definition and configure selector
   * Handles element references from the data collection and parameter substitution
   *
   * Label formats:
   * - "{ElementName}" - Reference to element in collection
   * - "{ElementName(param1, param2)}" - Element with parameters
   * - "direct-selector" - Direct CSS selector
   */
  private setElement(): void {
    // Extract element name from braces (e.g., "{Button}" -> "{Button}")
    this.name = this.label?.match(/{.*}/)?.[0];

    if (!this.name) {
      // No element reference found, treat label as direct selector
      this.selector = this.label;
      return;
    }

    // Extract element name without braces and parameters for lookup
    const elementToFind = this.name
      .replace(/{|}/g, "") // Remove braces
      .replace(/\([^)]*\)/g, ""); // Remove parameter block

    // Find matching element in the elements collection using normalized comparison
    this.key = h.getNormalizedValueInList(
      elementToFind,
      Object.keys(Cypress.sdt.data.elements)
    );

    if (!this.key) {
      // Element not found in collection, use name without braces as selector
      this.selector = this.name.replace(/{|}/g, "");
    } else {
      // Element found in collection, process it
      this.processElementFromCollection();
    }

    // Add visibility constraint to selector if needed
    if (this.isVisible && this.selector) {
      this.selector = this.selector + ":visible";
    }
  }

  /**
   * Processes an element found in the elements collection
   * Handles parameter substitution and tracking
   */
  private processElementFromCollection(): void {
    if (!this.key || !this.name) return;

    // Extract and parse arguments from the element name
    const argsBlock = this.name.match(/\(.*\)/)?.[0];
    const args = argsBlock ? argsBlock.replace(/[()]/g, "").split(/ *, */) : [];

    // Track usage of this element for reporting
    this.trackElementUsage();

    // Get the element definition from the collection
    const foundElement: ElementData = Cypress.sdt.data.elements[this.key];
    if (!foundElement) {
      console.warn(`Element data not found for key: ${this.key}`);
      return;
    }

    // Set element properties from the collection
    this.selector = foundElement.selector;
    this.locator = foundElement.locator || "byContent";
    this.root = foundElement.root || "body";

    // Replace parameter placeholders in the selector
    this.substituteParameters(args);
  }

  /**
   * Tracks the usage of this element for reporting purposes
   */
  private trackElementUsage(): void {
    if (!this.key) return;

    if (!Cypress.sdt.results.usedElements?.includes(this.key)) {
      Cypress.sdt.results.usedElements.push(this.key);
      Cypress.sdt.results.usedElements.sort();
    }
  }

  /**
   * Substitutes parameter placeholders in the selector with provided arguments
   * @param args - Array of argument values to substitute
   */
  private substituteParameters(args: string[]): void {
    if (!this.selector) return;

    let argIndex = 0;
    this.selector = this.selector.replace(/\$[\w\d]+/g, (match) => {
      const replacement = args[argIndex] ?? match;
      argIndex++;
      return replacement;
    });
  }

  /**
   * Extracts index information from the label
   * Index format: #number# (e.g., "#2#" for second element)
   */
  private setIndex(): void {
    const match = this.label.match(/#(.*)#/);
    this.index = match?.[1];

    // Remove index from label for further processing
    if (match?.[0]) {
      this.label = this.label.replace(match[0], "");
    }
  }

  /**
   * Extracts content text from the label after removing element references and indices
   * Content is the text that should be matched within the element
   */
  private setContent(): void {
    // Remove element reference and index, then trim whitespace
    this.content = this.label
      ?.replace(/{.*}/, "") // Remove element reference
      .replace(/#.*#/, "") // Remove index
      .trim();

    if (this.content) {
      this.setContentWithIcons();
    }
  }

  /**
   * Applies icon decorations to the content text
   * Icons can come from step-level configuration or element-specific configuration
   */
  private setContentWithIcons(): void {
    this.getIcon(this.key, this.content);

    const leftIcon = this.icon?.left || "";
    const rightIcon = this.icon?.right || "";

    if (this.content) {
      this.content = leftIcon + this.content + rightIcon;
    }
  }

  /**
   * Retrieves icon configuration for the element and content combination
   * @param element - Element key to look up icons for
   * @param content - Content text to look up icons for
   */
  private getIcon(element?: string, content?: string): void {
    // Check for step-level icon override first
    if (
      Cypress.sdt.current.step.icon?.left ||
      Cypress.sdt.current.step.icon?.right
    ) {
      this.icon = {
        left: Cypress.sdt.current.step.icon.left,
        right: Cypress.sdt.current.step.icon.right,
      };
      return;
    }

    // Look up element and content specific icons
    if (element && content) {
      const iconKey = Cypress.sdt.icons[element]?.[content];
      if (iconKey) {
        this.icon = {
          left: iconKey.left,
          right: iconKey.right,
        };
      }
    }
  }

  /**
   * Retrieves the Cypress element using the configured selector and locator strategy
   * @param parent - Parent Cypress chainable to search within
   * @param validation - Optional validation function to apply to the found element
   * @returns Cypress chainable for the located element
   */
  public get(
    parent: Cypress.Chainable<JQuery<HTMLElement>>,
    validation?: (element: JQuery<HTMLElement>) => void
  ): Cypress.Chainable<JQuery<HTMLElement>> {
    // Use direct selector if no custom locator is specified
    if (!this.locator) {
      return parent
        .find(this.selector!)
        .should(($elem: JQuery<HTMLElement>) => {
          if (this.isLast && validation) {
            validation($elem);
          }
        });
    }

    // Use custom locator strategy
    const locators = { ...coreLocators, ...Cypress.sdt.extension?.locators };
    const locator = h.getObjectFieldValueWithNormalizedKey(
      locators,
      this.locator
    );

    if (!locator) {
      throw new Error(`Locator strategy '${this.locator}' not found`);
    }

    return locator(parent, this, validation);
  }
}
