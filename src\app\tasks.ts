/**
 * Task loader module for dynamically importing project-specific tasks
 *
 * This module provides a centralized way to load task definitions from
 * project extensions based on the APP environment variable. It supports
 * multiple project organizations and provides comprehensive error handling.
 */

// Type definitions for better type safety
interface ITaskModule {
  default: Record<string, (...args: any[]) => any>;
}

interface IProjectConfig {
  organization: string;
  project: string;
}

/**
 * Map of available projects with their organizational structure
 * This allows for flexible project organization beyond just 'riverstar'
 */
const PROJECT_MAP: Record<string, IProjectConfig> = {
  // Riverstar projects
  ce: { organization: "riverstar", project: "ce" },
  billing: { organization: "riverstar", project: "billing" },
  pwv: { organization: "riverstar", project: "pwv" },
  riverstar: { organization: "riverstar", project: "riverstar" },
  srm: { organization: "riverstar", project: "srm" },

  // Dorian Solutions projects
  jba: { organization: "dorian solutions", project: "jba" },

  // Other projects
  rwa: { organization: "other", project: "rwa" },
};

/**
 * Loads tasks for the specified project
 * @param projectName - The name of the project to load tasks for
 * @returns Promise resolving to the project's task definitions
 * @throws Error if project is not found or tasks fail to load
 */
async function loadProjectTasks(
  projectName: string
): Promise<Record<string, (...args: any[]) => any>> {
  if (!projectName) {
    throw new Error(
      "Project name is required. Please set the APP environment variable."
    );
  }

  const projectConfig = PROJECT_MAP[projectName];
  if (!projectConfig) {
    const availableProjects = Object.keys(PROJECT_MAP).join(", ");
    throw new Error(
      `Project '${projectName}' not found. Available projects: ${availableProjects}`
    );
  }

  const { organization, project } = projectConfig;
  const tasksPath = `../../projects/${organization}/${project}/extension/tasks`;

  try {
    const taskModule: ITaskModule = await import(tasksPath);

    if (!taskModule.default) {
      throw new Error(
        `Tasks module for project '${projectName}' does not export a default object`
      );
    }

    // Validate that the exported object contains callable functions
    const tasks = taskModule.default;
    const taskNames = Object.keys(tasks);

    if (taskNames.length === 0) {
      console.warn(`Warning: No tasks found for project '${projectName}'`);
    }

    return tasks;
  } catch (error) {
    // Enhanced error message with more context
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    throw new Error(
      `Failed to load tasks for project '${projectName}' from '${tasksPath}': ${errorMessage}`
    );
  }
}

/**
 * Main export: Immediately invoked async function that loads tasks
 * based on the APP environment variable
 */
export default (async () => {
  try {
    const projectName = process.env.APP;
    return await loadProjectTasks(projectName);
  } catch (error) {
    // Log error for debugging while still throwing for proper error propagation
    console.error(
      "Task loading failed:",
      error instanceof Error ? error.message : error
    );
    throw error;
  }
})();
